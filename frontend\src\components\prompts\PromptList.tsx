import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { promptService } from '../../services/firestore';
import type { Prompt } from '../../types';
import { FileText, Edit, Trash2, Play, Calendar, Tag } from 'lucide-react';
import { Button } from '../common/Button';
import { LoadingSpinner } from '../common/LoadingSpinner';
import { VirtualizedGrid, useVirtualizationParams } from '../common/VirtualizedList';
import { withPerformanceProfiler } from '../../utils/performanceProfiler';

interface PromptListProps {
  onEditPrompt: (prompt: Prompt) => void;
  refreshTrigger?: number;
}

// Memoized PromptCard component to prevent unnecessary re-renders
const PromptCard = React.memo<{
  prompt: Prompt;
  onEdit: (prompt: Prompt) => void;
  onDelete: (id: string) => void;
  onExecute: (id: string) => void;
}>(({ prompt, onEdit, onDelete, onExecute }) => {
  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg hover:shadow-md transition-shadow">
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <FileText className="h-6 w-6 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white truncate">
              {prompt.title}
            </h3>
          </div>
          {prompt.isPublic && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              Public
            </span>
          )}
        </div>

        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
          {prompt.description || 'No description provided'}
        </p>

        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Tag className="h-4 w-4 mr-1" />
            <span className="font-medium">{prompt.category}</span>
          </div>
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <Calendar className="h-4 w-4 mr-1" />
            {prompt.createdAt instanceof Date
              ? prompt.createdAt.toLocaleDateString()
              : new Date(prompt.createdAt.seconds * 1000).toLocaleDateString()}
          </div>
          {prompt.tags && prompt.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {prompt.tags.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {tag}
                </span>
              ))}
              {prompt.tags.length > 3 && (
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  +{prompt.tags.length - 3} more
                </span>
              )}
            </div>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <Button
              variant="primary"
              size="sm"
              onClick={() => onExecute(prompt.id)}
            >
              <Play className="h-4 w-4 mr-1" />
              Execute
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(prompt)}
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(prompt.id)}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
});

const PromptListComponent: React.FC<PromptListProps> = ({
  onEditPrompt,
  refreshTrigger
}) => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  const [prompts, setPrompts] = useState<Prompt[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // Virtualization setup
  const containerRef = useRef<HTMLDivElement>(null);
  const CARD_HEIGHT = 320; // Approximate height of each prompt card
  const CARDS_PER_ROW = 3; // Number of cards per row on large screens
  const VIRTUALIZATION_THRESHOLD = 15; // Use virtualization when more than 15 items

  const { height: containerHeight } = useVirtualizationParams(containerRef, CARD_HEIGHT, 600);

  const categories = [
    'General',
    'Customer Support',
    'Content Creation',
    'Data Analysis',
    'Code Generation',
    'Translation',
    'Summarization',
    'Question Answering'
  ];

  const loadPrompts = useCallback(async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      const userPrompts = await promptService.getUserPrompts(currentUser.uid);
      setPrompts(userPrompts);
    } catch (error) {
      console.error('Error loading prompts:', error);
    } finally {
      setLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    if (currentUser) {
      loadPrompts();
    }
  }, [currentUser, refreshTrigger, loadPrompts]);

  // Memoize event handlers to prevent unnecessary re-renders
  const handleDeletePrompt = useCallback(async (promptId: string) => {
    if (!currentUser) return;

    if (window.confirm('Are you sure you want to delete this prompt?')) {
      try {
        await promptService.deletePrompt(currentUser.uid, promptId);
        setPrompts(prev => prev.filter(p => p.id !== promptId));
      } catch (error) {
        console.error('Error deleting prompt:', error);
      }
    }
  }, [currentUser]);

  // Memoize filtered prompts to prevent unnecessary recalculations
  const filteredPrompts = useMemo(() => {
    return prompts.filter(prompt => {
      const matchesSearch = prompt.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           prompt.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           prompt.content.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = !selectedCategory || prompt.category === selectedCategory;

      const matchesTags = selectedTags.length === 0 ||
                         selectedTags.some(tag => prompt.tags.includes(tag));

      return matchesSearch && matchesCategory && matchesTags;
    });
  }, [prompts, searchTerm, selectedCategory, selectedTags]);

  // Memoize all tags extraction
  const allTags = useMemo(() => {
    return Array.from(new Set(prompts.flatMap(p => p.tags)));
  }, [prompts]);

  // Virtualized item renderer for large lists
  const renderVirtualizedItem = useCallback(({
    index,
    style,
    data
  }: {
    index: number;
    style: React.CSSProperties;
    data: Prompt[];
  }) => {
    const prompt = data[index];
    return (
      <div style={style} className="p-3">
        <PromptCard
          prompt={prompt}
          onEdit={onEditPrompt}
          onDelete={handleDeletePrompt}
          onExecute={(id) => navigate(`/prompts/${id}/execute`)}
        />
      </div>
    );
  }, [onEditPrompt, handleDeletePrompt, navigate]);

  // Determine whether to use virtualization
  const shouldUseVirtualization = filteredPrompts.length > VIRTUALIZATION_THRESHOLD;

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
        <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
          {/* Search */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Search
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search prompts..."
              className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            />
          </div>

          {/* Category Filter */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Category
            </label>
            <select
              id="category"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          {/* Tag Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tags
            </label>
            <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
              {allTags.map(tag => (
                <button
                  key={tag}
                  onClick={() => {
                    if (selectedTags.includes(tag)) {
                      setSelectedTags(selectedTags.filter(t => t !== tag));
                    } else {
                      setSelectedTags([...selectedTags, tag]);
                    }
                  }}
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium transition-colors ${
                    selectedTags.includes(tag)
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                      : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        Showing {filteredPrompts.length} of {prompts.length} prompts
      </div>

      {/* Prompt Cards */}
      {filteredPrompts.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No prompts found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {prompts.length === 0 
              ? "Get started by creating your first prompt."
              : "Try adjusting your search criteria."
            }
          </p>
        </div>
      ) : shouldUseVirtualization ? (
        <div ref={containerRef} className="h-96">
          <VirtualizedGrid
            items={filteredPrompts}
            height={containerHeight}
            itemHeight={CARD_HEIGHT}
            itemsPerRow={CARDS_PER_ROW}
            renderItem={renderVirtualizedItem}
            className="w-full"
            gap={24}
          />
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {filteredPrompts.map((prompt) => (
            <PromptCard
              key={prompt.id}
              prompt={prompt}
              onEdit={onEditPrompt}
              onDelete={handleDeletePrompt}
              onExecute={(id) => navigate(`/prompts/${id}/execute`)}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Export with performance profiling in development
export const PromptList = withPerformanceProfiler(PromptListComponent, 'PromptList');
