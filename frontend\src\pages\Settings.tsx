import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { LoadingSpinner } from '../components/common/LoadingSpinner';
import { ApiKeyModal } from '../components/settings/ApiKeyModal';
import { settingsService, type UserSettings } from '../services/settingsService';
import {
  UserIcon,
  KeyIcon,
  BellIcon,
  CreditCardIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckIcon,
  XMarkIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

export const Settings: React.FC = () => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [errors, setErrors] = useState<string[]>([]);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);

  useEffect(() => {
    const fetchSettings = async () => {
      if (!currentUser) return;

      setLoading(true);
      try {
        const userSettings = await settingsService.getOrCreateUserSettings(currentUser);
        setSettings(userSettings);
      } catch (error) {
        console.error('Failed to fetch settings:', error);
        setErrors(['Failed to load settings. Please try refreshing the page.']);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [currentUser]);

  const handleSaveSettings = async () => {
    if (!currentUser || !settings) return;

    setSaving(true);
    setErrors([]);
    setSuccessMessage('');

    try {
      // Validate settings
      const validation = settingsService.validateSettings(settings);
      if (!validation.isValid) {
        setErrors(validation.errors);
        return;
      }

      await settingsService.updateUserSettings(currentUser.uid, settings);
      setSuccessMessage('Settings saved successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Failed to save settings:', error);
      setErrors(['Failed to save settings. Please try again.']);
    } finally {
      setSaving(false);
    }
  };

  const handleAddApiKey = async (apiKeyData: { name: string; provider: string; key: string }) => {
    if (!currentUser || !settings) return;

    try {
      const newKeyId = await settingsService.addApiKey(currentUser.uid, apiKeyData);

      // Update local state
      const newApiKey = {
        id: newKeyId,
        name: apiKeyData.name,
        provider: apiKeyData.provider,
        masked: settingsService.maskApiKey(apiKeyData.key),
        lastUsed: new Date().toISOString(),
        isActive: true,
        createdAt: new Date().toISOString(),
      };

      setSettings({
        ...settings,
        apiKeys: [...settings.apiKeys, newApiKey]
      });

      setSuccessMessage('API key added successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Failed to add API key:', error);
      throw error; // Re-throw to let the modal handle the error
    }
  };

  const handleRemoveApiKey = async (apiKeyId: string) => {
    if (!currentUser || !settings) return;

    try {
      await settingsService.removeApiKey(currentUser.uid, apiKeyId);
      setSettings({
        ...settings,
        apiKeys: settings.apiKeys.filter(key => key.id !== apiKeyId)
      });
      setSuccessMessage('API key removed successfully!');
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error) {
      console.error('Failed to remove API key:', error);
      setErrors(['Failed to remove API key. Please try again.']);
    }
  };

  const tabs = [
    { id: 'profile', name: 'Profile', icon: UserIcon },
    { id: 'api-keys', name: 'API Keys', icon: KeyIcon },
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'privacy', name: 'Privacy', icon: ShieldCheckIcon },
    { id: 'billing', name: 'Billing', icon: CreditCardIcon }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64" data-testid="settings-loading">
        <LoadingSpinner data-testid="loading-spinner" />
      </div>
    );
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Failed to load settings</h3>
        <p className="mt-1 text-sm text-gray-500">
          Please try refreshing the page.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Settings
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage your account preferences and configuration
          </p>
        </div>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                {errors.length === 1 ? 'Error' : 'Errors'}
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <ul className="list-disc pl-5 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      <div className="lg:grid lg:grid-cols-12 lg:gap-x-5">
        {/* Sidebar */}
        <aside className="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'bg-gray-50 text-indigo-700 hover:text-indigo-700 hover:bg-gray-50'
                      : 'text-gray-900 hover:text-gray-900 hover:bg-gray-50'
                  } group rounded-md px-3 py-2 flex items-center text-sm font-medium w-full text-left`}
                >
                  <Icon
                    className={`${
                      activeTab === tab.id ? 'text-indigo-500' : 'text-gray-400'
                    } flex-shrink-0 -ml-1 mr-3 h-6 w-6`}
                  />
                  <span className="truncate">{tab.name}</span>
                </button>
              );
            })}
          </nav>
        </aside>

        {/* Main content */}
        <div className="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
              <div className="md:grid md:grid-cols-3 md:gap-6">
                <div className="md:col-span-1">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Profile</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Update your personal information and preferences.
                  </p>
                </div>
                <div className="mt-5 md:mt-0 md:col-span-2">
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Display Name
                      </label>
                      <input
                        type="text"
                        value={settings.profile.displayName}
                        onChange={(e) => setSettings({
                          ...settings,
                          profile: { ...settings.profile, displayName: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        placeholder="Enter your display name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Email
                      </label>
                      <input
                        type="email"
                        value={settings.profile.email}
                        disabled
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 sm:text-sm"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Email cannot be changed. Contact support if needed.
                      </p>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Timezone
                      </label>
                      <select
                        value={settings.profile.timezone}
                        onChange={(e) => setSettings({
                          ...settings,
                          profile: { ...settings.profile, timezone: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option value="America/New_York">Eastern Time</option>
                        <option value="America/Chicago">Central Time</option>
                        <option value="America/Denver">Mountain Time</option>
                        <option value="America/Los_Angeles">Pacific Time</option>
                        <option value="UTC">UTC</option>
                        <option value="Europe/London">London</option>
                        <option value="Europe/Paris">Paris</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Language
                      </label>
                      <select
                        value={settings.profile.language}
                        onChange={(e) => setSettings({
                          ...settings,
                          profile: { ...settings.profile, language: e.target.value }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="ja">Japanese</option>
                        <option value="zh">Chinese</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* API Keys Tab */}
          {activeTab === 'api-keys' && (
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
              <div className="md:grid md:grid-cols-3 md:gap-6">
                <div className="md:col-span-1">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">API Keys</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Manage your AI provider API keys for prompt execution.
                  </p>
                </div>
                <div className="mt-5 md:mt-0 md:col-span-2">
                  <div className="space-y-4">
                    {settings.apiKeys.map((apiKey) => (
                      <div key={apiKey.id} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-sm font-medium text-gray-900">{apiKey.name}</h4>
                            <p className="text-sm text-gray-500">{apiKey.provider}</p>
                            <p className="text-xs text-gray-400">
                              {apiKey.masked} • Last used: {new Date(apiKey.lastUsed).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="flex space-x-2">
                            <button className="text-sm text-indigo-600 hover:text-indigo-900">
                              Edit
                            </button>
                            <button
                              onClick={() => handleRemoveApiKey(apiKey.id)}
                              className="text-sm text-red-600 hover:text-red-900"
                            >
                              Delete
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                    <button
                      onClick={() => setShowApiKeyModal(true)}
                      className="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400"
                    >
                      <PlusIcon className="mx-auto h-8 w-8 text-gray-400" />
                      <span className="mt-2 block text-sm font-medium text-gray-900">
                        Add API Key
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notifications Tab */}
          {activeTab === 'notifications' && (
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
              <div className="md:grid md:grid-cols-3 md:gap-6">
                <div className="md:col-span-1">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Notifications</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Choose what notifications you want to receive.
                  </p>
                </div>
                <div className="mt-5 md:mt-0 md:col-span-2">
                  <div className="space-y-4">
                    {Object.entries(settings.notifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium text-gray-900">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </label>
                          <p className="text-xs text-gray-500">
                            {key === 'emailNotifications' && 'Receive email notifications for important updates'}
                            {key === 'promptSharing' && 'Get notified when someone shares a prompt with you'}
                            {key === 'systemUpdates' && 'Receive notifications about system updates and maintenance'}
                            {key === 'weeklyDigest' && 'Get a weekly summary of your activity'}
                            {key === 'marketingEmails' && 'Receive promotional emails and product updates'}
                            {key === 'securityAlerts' && 'Get notified about security-related events'}
                          </p>
                        </div>
                        <button
                          onClick={() => setSettings({
                            ...settings,
                            notifications: { ...settings.notifications, [key]: !value }
                          })}
                          className={`${
                            value ? 'bg-indigo-600' : 'bg-gray-200'
                          } relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
                        >
                          <span
                            className={`${
                              value ? 'translate-x-5' : 'translate-x-0'
                            } pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
                          />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Privacy Tab */}
          {activeTab === 'privacy' && (
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
              <div className="md:grid md:grid-cols-3 md:gap-6">
                <div className="md:col-span-1">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Privacy</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Control your privacy settings and data sharing preferences.
                  </p>
                </div>
                <div className="mt-5 md:mt-0 md:col-span-2">
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Profile Visibility
                      </label>
                      <select
                        value={settings.privacy.profileVisibility}
                        onChange={(e) => setSettings({
                          ...settings,
                          privacy: { ...settings.privacy, profileVisibility: e.target.value as 'public' | 'private' }
                        })}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                      >
                        <option value="public">Public</option>
                        <option value="private">Private</option>
                      </select>
                      <p className="mt-1 text-xs text-gray-500">
                        Control who can see your profile information
                      </p>
                    </div>

                    {Object.entries(settings.privacy).filter(([key]) => key !== 'profileVisibility').map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between">
                        <div>
                          <label className="text-sm font-medium text-gray-900">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </label>
                          <p className="text-xs text-gray-500">
                            {key === 'allowAnalytics' && 'Help us improve by sharing anonymous usage data'}
                            {key === 'shareUsageData' && 'Share usage statistics with our partners'}
                            {key === 'showOnlineStatus' && 'Let others see when you are online'}
                            {key === 'allowDirectMessages' && 'Allow other users to send you direct messages'}
                          </p>
                        </div>
                        <button
                          onClick={() => setSettings({
                            ...settings,
                            privacy: { ...settings.privacy, [key]: !value }
                          })}
                          className={`${
                            value ? 'bg-indigo-600' : 'bg-gray-200'
                          } relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
                        >
                          <span
                            className={`${
                              value ? 'translate-x-5' : 'translate-x-0'
                            } pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
                          />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Billing Tab */}
          {activeTab === 'billing' && (
            <div className="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
              <div className="md:grid md:grid-cols-3 md:gap-6">
                <div className="md:col-span-1">
                  <h3 className="text-lg font-medium leading-6 text-gray-900">Billing</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Manage your subscription and billing information.
                  </p>
                </div>
                <div className="mt-5 md:mt-0 md:col-span-2">
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-lg font-medium text-gray-900 capitalize">
                            {settings.billing.plan} Plan
                          </h4>
                          <p className="text-sm text-gray-500">
                            Status: <span className="capitalize">{settings.billing.status}</span>
                          </p>
                          {settings.billing.currentPeriodEnd && (
                            <p className="text-sm text-gray-500">
                              Next billing: {new Date(settings.billing.currentPeriodEnd).toLocaleDateString()}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          {settings.billing.plan === 'free' ? (
                            <button className="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                              Upgrade Plan
                            </button>
                          ) : (
                            <button className="bg-gray-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-700">
                              Manage Subscription
                            </button>
                          )}
                        </div>
                      </div>
                    </div>

                    {settings.billing.paymentMethod && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Payment Method</h4>
                        <div className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <CreditCardIcon className="h-6 w-6 text-gray-400 mr-3" />
                              <div>
                                <p className="text-sm font-medium text-gray-900">
                                  {settings.billing.paymentMethod.type} ending in {settings.billing.paymentMethod.last4}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Expires {settings.billing.paymentMethod.expiryMonth}/{settings.billing.paymentMethod.expiryYear}
                                </p>
                              </div>
                            </div>
                            <button className="text-sm text-indigo-600 hover:text-indigo-900">
                              Update
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="border-t pt-6">
                      <h4 className="text-sm font-medium text-gray-900 mb-4">Plan Features</h4>
                      <div className="space-y-2">
                        {settings.billing.plan === 'free' && (
                          <>
                            <div className="flex items-center text-sm text-gray-600">
                              <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                              10 prompt executions per month
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                              Basic AI models
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <XMarkIcon className="h-4 w-4 text-gray-400 mr-2" />
                              Advanced AI models
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <XMarkIcon className="h-4 w-4 text-gray-400 mr-2" />
                              Priority support
                            </div>
                          </>
                        )}
                        {settings.billing.plan === 'pro' && (
                          <>
                            <div className="flex items-center text-sm text-gray-600">
                              <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                              Unlimited prompt executions
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                              All AI models
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                              Priority support
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                              Advanced analytics
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSaveSettings}
              disabled={saving}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {saving ? (
                <LoadingSpinner size="sm" className="mr-2" />
              ) : (
                <CheckIcon className="-ml-1 mr-2 h-5 w-5" />
              )}
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>

      {/* API Key Modal */}
      <ApiKeyModal
        isOpen={showApiKeyModal}
        onClose={() => setShowApiKeyModal(false)}
        onSave={handleAddApiKey}
      />
    </div>
  );
};
